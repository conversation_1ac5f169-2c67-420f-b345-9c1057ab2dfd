/* Temporariamente SEM scoped e SEM :deep() */
/* Em <style> global */
.api-filled-border[data-v-c3b259] {
    border-color: #0d6efd !important;
    /* Reduzi o box-shadow para um valor mais comum, ajuste se necessário */
    box-shadow: 0 0 0 0.85rem rgba(13, 110, 253, 0.25) !important;
    /* transition: ... */
}

/* Regra .api-filled-border + .bootstrap-select > .dropdown-toggle REMOVIDA */
.api-filled-border+.bootstrap-select>.dropdown-toggle[data-v-c3b259] {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.85rem rgba(13, 110, 253, 0.25) !important;
    transition: border-color .5s ease-out, box-shadow .5s ease-out;
}
[data-v-c3b259]:deep(.api-filled-border) {
    /* Ou só .api-filled-border se global */
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.85rem rgba(13, 110, 253, 0.25) !important;
}.red-text[data-v-a42a80] {
    color: red;
}
.alert-anim[data-v-a42a80] {
    animation: alert-anim-data-v-a42a80 10s ease forwards;
}
@keyframes alert-anim-data-v-a42a80 {
0% {
        scale: .9;
        opacity: 0%;
}
5%,
    95% {
        scale: 1;
        opacity: 100%;
}
100% {
        scale: .9;
        opacity: 0%;
}
}
.check[data-v-a42a80] {
    cursor: pointer;
    transition: .5s;
}
.check-success[data-v-a42a80] {
    color: #3DB23D;
}
.check-warn[data-v-a42a80] {
    color: #ddd;
}
.border[data-v-a42a80] {
    transition: .5s;
}
.border-success[data-v-a42a80] {
    border-right: .5rem solid #3DB23D;
}
.border-warn[data-v-a42a80] {
    border-right: .5rem solid #ddd;
}
.justify-content-between[data-v-a42a80] {
    justify-content: space-between;
}
.align-items-middle[data-v-a42a80] {
    justify-content: center;
    align-items: center;
}
.template[data-v-a42a80] {
    margin-top: 1.5rem;
    border-left: 1px solid lightgrey;
}
.panel[data-v-a42a80] {
    transition: .25s;
}
.panel[data-v-a42a80]:hover {
    box-shadow: 0 15px 30px rgb(0 0 0 / 15%);
}
.panel-custom[data-v-a42a80] {
    background: unset !important;
    margin-bottom: 0 !important;
    box-shadow: unset !important;
    -webkit-box-shadow: unset !important;
}
.d-flex[data-v-a42a80] {
    display: flex;
}
.pt-0[data-v-a42a80] {
    padding-top: 0 !important;
}
.pe-0[data-v-a42a80] {
    padding-right: 0 !important;
}
.ps-5[data-v-a42a80] {
    padding-left: 2rem;
}
.px-4[data-v-a42a80] {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}
.pt-5[data-v-a42a80] {
    padding-top: 2rem;
}
.pb-4[data-v-a42a80] {
    padding-bottom: 1.5rem;
}.vld-shown {
  overflow: hidden;
}

.vld-overlay {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  align-items: center;
  display: none;
  justify-content: center;
  overflow: hidden;
  z-index: 9999;
}

.vld-overlay.is-active {
  display: flex;
}

.vld-overlay.is-full-page {
  z-index: 9999;
  position: fixed;
}

.vld-overlay .vld-background {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #fff;
  opacity: 0.5;
}

.vld-overlay .vld-icon, .vld-parent {
  position: relative;
}

.mx-2[data-v-4eb270] {
    margin-left: 1.5rem;
    margin-right: 1.5rem;
}
.mb-2[data-v-4eb270] {
    margin-bottom: 1.5rem;
}

/* Temporariamente SEM scoped e SEM :deep() */
.api-filled-border[data-v-4eb270] {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 1.20rem rgba(13, 110, 253, 0.25) !important;
    transition: border-color .5s ease-out, box-shadow .5s ease-out;
}
.api-filled-border+.bootstrap-select>.dropdown-toggle[data-v-4eb270] {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 1.20rem rgba(13, 110, 253, 0.25) !important;
    transition: border-color .5s ease-out, box-shadow .5s ease-out;
}
[data-v-4eb270]:deep(.api-filled-border) {
    /* Ou só .api-filled-border se global */
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 1.20rem rgba(13, 110, 253, 0.25) !important;
}