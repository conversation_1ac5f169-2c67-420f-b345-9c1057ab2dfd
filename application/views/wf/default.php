<script>
    ! function(e, o) {
        void 0 === e && void 0 !== window && (e = window), "function" == typeof define && define.amd ? define(["jquery"], function(e) {
            return o(e)
        }) : "object" == typeof module && module.exports ? module.exports = o(require("jquery")) : o(e.jQuery)
    }(this, function(e) {
        e.fn.selectpicker.defaults = {
            noneSelectedText: "Nada selecionado",
            noneResultsText: "Nada encontrado contendo {0}",
            countSelectedText: "Selecionado {0} de {1}",
            maxOptionsText: ["Limite excedido (m\xe1x. {n} {var})", "Limite do grupo excedido (m\xe1x. {n} {var})", ["itens", "item"]],
            multipleSeparator: ", ",
            selectAllText: "Todos",
            deselectAllText: "Limpar"
        }
    });
</script>

<style scoped>
    .table-atribute {
        max-height: 550px;
        /* Defina o tamanho máximo que a tabela pode ter */
        overflow-y: auto;
        /* Adicione uma barra de rolagem vertical */
    }

    .table th {
        position: sticky;
        top: 0;
        z-index: 100;
        background-color: white;
        box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.4);
        padding: 8px 16px;
        text-align: left;
    }

    .table {
        width: 100%;
        /* Garanta que a tabela ocupe toda a largura disponível */
    }

    .dynamic-column-th {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .table th,
    .table td {
        padding: 8px 16px;
    }

    .table th.fixed-header {
        position: sticky;
        top: 0;
        z-index: 100;
        background-color: white;
        box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.4);
        padding: 8px 16px;
        text-align: left;
    }
</style>

<script type="text/javascript">
    var item = {

        edit: function() {
            checked = $('input[type="checkbox"][name="item[]"]:checked');

            if (checked.length == 0) {
                swal('Atenção', 'Selecione um item para realizar a edição', 'warning');
                return false;
            }

            if (checked.length > 1) {
                swal('Atenção', 'Só é possível editar um item por vez', 'warning');
                return false;
            }

            item_part_number = encodeURIComponent($(checked).val());
            item_id_empresa = $(checked).attr('rel');
            item_estab = encodeURIComponent($(checked).attr('data-estabelecimento'));

            location.href = '<?php echo base_url() . "cadastros/mestre_itens/editar?part_number=" ?>' + item_part_number + '&id_empresa=' + item_id_empresa + '&estabelecimento=' + item_estab;
        },

        remove: function() {
            checked = $('input[type=checkbox]:checked');

            if (checked.length == 0) {
                swal('Atenção', 'Primeiro você deve selecionar o(s) íten(s) que deseja excluir.', 'warning');
                return false;
            }

            var lista_itens = new Array();

            $('input[type="checkbox"][name="item[]"]:checked').each(function() {
                var obj = {
                    'part_number': $(this).val(),
                    'estabelecimento': $(this).attr('data-estabelecimento')
                };

                lista_itens.push(obj);
            });

            var post_data = {
                'items': lista_itens
            };

            swal({
                title: "Atenção!",
                text: "Você deseja excluir os registros selecionados?",
                type: "warning",
                confirmButtonText: "OK",
                cancelButtonText: "Cancelar",
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false
            }).then(function() {
                $.post('<?php echo base_url("cadastros/mestre_itens/excluir") ?>',
                    post_data,
                    function(data, status, xhr) {
                        var json = $.parseJSON(data);

                        if (json.status == true) {
                            window.location.reload();
                        } else {
                            swal('Atenção', "Ops... um erro aconteceu, recarregue a página.", 'warning');
                        }
                    }
                );
            });
        }
    };


    $(document).ready(function() {
        $('.click-select').on('click', function(e) {
            if (e.target.nodeName != 'INPUT') {
                $(this).find('input').click();
            }
        })

        $('.btn-edit-item').on('click', function(e) {
            item.edit();
        })

        $('.btn-remove-item').on('click', function(e) {
            item.remove();
        })

        $('#btn-transferencia-owner').click(function(e) {
            $("#message_user_transfer_owner").empty();
            let checked_itens_to_owners = $('input[type="checkbox"][name="item[]"]:checked');

            if (checked_itens_to_owners.length == 0) {
                swal('Atenção', 'Selecione no mínimo um item para transferir', 'warning');
                return false;
            }

            console.log(checked_itens_to_owners);
            $("#total_itens_owners").text(checked_itens_to_owners.length);
        });
    })
</script>
<link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/atribuir-diana.css?version=' . config_item('assets_version')) ?>" type="text/css" media="screen" />

<style>
    .table.table-striped .descricao {
        width: 35% !important;
    }

    .table.table-striped .owner {
        max-width: 7% !important;
    }

    #resultados-e-paginacao {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    #resultados-e-paginacao #resultado-contagem {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    #resultados-e-paginacao #resultado-contagem #resultado-contagem-titulo {
        font-size: 20px;
    }

    #resultados-e-paginacao #resultado-contagem .resultado-contagem-texto {
        font-size: 15px;
    }
</style>


<div class="page-header">
    <div id="ajax_validate"></div>
    <h2>
        Análise de Atributos
    </h2>

    <hr />



    <div class="row">
        <div class="col-md-12">
            <?php $this->load->view('wf/default_filter'); ?>
        </div>
    </div>




</div>

<div id="resultados-e-paginacao">
    <?php if (!empty($list) && isset($total_ncms) && isset($total_itens)) { ?>
        <div id="resultado-contagem">
            <p id="resultado-contagem-titulo" class="mb-0">Resultado:</p>
            <span class="label label-primary resultado-contagem-texto">NCMs: <?php echo $total_ncms; ?></span>
            <span class="label label-primary resultado-contagem-texto">Part Numbers: <?php echo $total_itens; ?></span>
        </div>
    <?php } ?>
    <?php if (isset($pagination)) { ?>
        <div class="controls">
            <?php echo (!empty($pagination) ? $pagination : '') ?>
        </div>
    <?php } ?>
</div>

<div id="dados_busca" class="row">
    <div class="col-md-12">
        <div class="form-group" style="width: 100%;">
            <div id="app-wf-agrupamento-ncm">
                <?php echo (!empty($list) ? '' : '<div class="pull-center" style="text-align-last: center;"><p class="lead">Nenhum registro encontrado</p></div>') ?>

                <v-wf-agrupamento-ncm
                    :data='<?php echo json_encode($list); ?>'
                    :homologar-sem-obrigatorios='<?php echo $has_flag_missing_mandatory; ?>'
                    :can-homologar='<?php echo $can_homologar; ?>'>
                </v-wf-agrupamento-ncm>
            </div>
            <script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/wf-agrupamento-ncm.js?version=' . config_item('assets_version')) ?>"></script>
            <link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/wf-agrupamento-ncm.css?version=' . config_item('assets_version')) ?>" type="text/css" media="screen" />
        </div>
    </div>
</div>


<?php if (isset($pagination)) { ?>
    <div class="controls">
        <div class="pull-right">
            <?php echo (!empty($pagination) ? $pagination : '') ?>
        </div>
    </div>
<?php } ?>

<script>
    $(document).ready(function() {
        $('#collapseOne').collapse('hide');

        if ($('#status_cassificacao_fiscal').val() != '' || ($('#evento').val().length != 0 && $('#estabelecimento').val() != '-1') || ($('#ncm_proposta').val().length != 0 && $('#ncm_proposta').val() != '-1') || ($('#prioridade').val().length != 0 && $('#prioridade').val() != '-1') || ($('#responsavel').val().length != 0 && $('#responsavel').val() != '-1') || ($('#status_integracao').val().length != 0 && $('#status_integracao').val() != '-1') || $('#status_atributos').val() != '' || $('#status_preenchimento').val() != '' || $('#owner').val() != '' || $('#estabelecimento').val() != '') {

            $('#collapseOne').collapse('show');
        }

        $('#pesquisar-btn, .page').on('click', function() {
            $('#loading-overlay').show();
            $('#loading-img-exp').show();
        })

        window.setTimeout(function() {
            $('#loading-img-exp').hide();
            $('#loading-overlay').hide();
        }, 1000);
    });

    $('#loading-overlay').show();
    var botaoExportar = document.getElementById("send_generate_log");
    if (botaoExportar) {
        botaoExportar.setAttribute("disabled", "disabled");
    }
    $('#loading-img-exp').show();
</script>

<style>
    .container {
        width: 1340px !important;
    }
    .valor-atualizado {
        border: 1px solid #007bff !important;
        box-shadow: 0 0 0 1.2rem rgba(0, 123, 255, 0.25);
        transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }
    .form-control {
        border: 1px solid #ced4da;
    }
</style>