<?php
require_once  APPPATH . 'libraries/Spout/Autoloader/autoload.php';

use Box\Spout\Reader\ReaderFactory;
use Box\Spout\Common\Type;

class <PERSON>ron extends CI_Controller
{

    public $CI;
    public function __construct()
    {
        parent::__construct();
        $this->CI =& get_instance();
        $this->db = $this->CI->db;
    }

    public function debug($message)
    {

        if (!has_role('sysadmin') && !$this->input->is_cli_request()) {
            show_permission();
        }

        if ($this->input->is_cli_request()) {
            echo $message . PHP_EOL;
        } else {
            echo $message . '<br />';
        }
    }

    public function save_xls($base64)
    {
        set_time_limit(0);

        $decoded = base64_decode($base64);

        $values = array();

        parse_str($decoded, $values);

        $this->load->library('upload');

        $reader = ReaderFactory::create(Type::XLSX);

        $reader->open($values['uploadpath']);

        $this->load->helper('text_helper');

        $id_empresa = $values['id_empresa'];

        //Índices de cada coluna na tabela:
        $idx = array(
            'part_number'   => 0,
            'descricao'     => 1
        );

        $this->load->library('gerador_xls_grupo_tarifario');

        $items = $this->gerador_xls_grupo_tarifario->iteratorSheet($reader, $idx, array('id_empresa' => $id_empresa));


        $filename = $this->gerador_xls_grupo_tarifario->generate_xls($items, false);

        $email_data['base_url'] = config_item('online_url');
        $email_data['title'] = 'Geração do XLSX concluída.';
        $email_data['url_download'] = config_item('online_url') . 'assets/tmp/' . $filename;

        $html = $this->load->view('templates/consulta_sugestao_grupo_tarifario', $email_data, TRUE);

        $this->load->model('usuario_model');
        $usuario = $this->usuario_model->get_entry($values['id_usuario']);

        $this->load->library('email');

        $this->email->from(config_item('mail_from_addr'));
        $this->email->to($usuario->email);

        $this->email->subject('[Gestão Tarifária] - Sugestão para Grupo Tarifário');

        $this->email->message($html);
        $this->email->send();
    }

    public function index()
    {
        if (!has_role('sysadmin') && !$this->input->is_cli_request()) {
            show_permission();
        }

        set_time_limit(0);
        @ob_implicit_flush(TRUE);
        @ob_end_flush();

        $this->load->model('ncm_model');
        $this->load->model('ex_tarifario_model');
        $this->load->model('nve_atributo_model');

        $this->db->query('SET NAMES binary');

        // Resolve o problema de memory leak
        $this->db->save_queries = FALSE;
        $this->db->db_debug     = FALSE;

        $this->load->library('benchmark');

        $this->benchmark->mark('code_start');

        $this->debug('Integração iniciada - ' . date('Y-m-d H:i:s'));

        $result_ex_tarif = $this->ex_tarifario_model->from_oracle_import_tec_ncm_ex_tarif();
        $this->debug('OK - ex_tarifario');

        $result_nve_atrb = $this->nve_atributo_model->from_oracle_import_nve_atributo();
        $this->debug('OK - nve_atributo');

        $result_nve_atrV = $this->nve_atributo_model->from_oracle_import_nve_atributo_valor();
        $this->debug('OK - nve_atributo_valor');

        $result_tec_ncm = $this->ncm_model->from_oracle_import_tec_ncm();
        $this->debug('OK - ncm_impostos');

        $result_destinatarios_tec_ncm = $this->envio_relatorios_destinatarios_tec($result_tec_ncm['ncms_alteradas']);
        $this->debug('OK - envio_relatorio_destinatarios_ncm');

        $result_ncm_validos = $this->ncm_model->update_ncm_validos();
        $this->debug('OK - NCMs válidos para II/IPI e NVE atualizados.');

        $this->debug('Integração finalizada - ' . date('Y-m-d H:i:s'));

        $this->benchmark->mark('code_end');

        $elapsed_time = $this->benchmark->elapsed_time('code_start', 'code_end');

        $this->debug('Tempo gasto: ' . $elapsed_time . ' segundos');

        $filename = APPPATH . '/logs/log-cronjob.txt';

        if ((file_exists($filename) && is_writable($filename)) || !file_exists($filename)) {
            $message = '--------------------------------------------------------------' . PHP_EOL;
            $message .= '[' . date('Y-m-d H:i:s') . '] - Integração com o Oracle/Tec' . PHP_EOL;;
            $message .= ' - NCM Impostos - Atualizados: ' . $result_tec_ncm['atualizados'] . ' - Inseridos: ' . $result_tec_ncm['inseridos'] . PHP_EOL;
            $message .= ' - Relatório TEC - Relatórios Gerados/Empresa: ' . $result_destinatarios_tec_ncm['empresas_relatorio'] . ' - Emails Enviados: ' . $result_destinatarios_tec_ncm['emails_enviados'] . PHP_EOL;
            $message .= ' - EX Tarifário - Atualizados: ' . $result_ex_tarif['atualizados'] . ' - Inseridos: ' . $result_ex_tarif['inseridos'] . PHP_EOL;
            $message .= ' - NVE Atributo - Atualizados: ' . $result_nve_atrb['atualizados'] . ' - Inseridos: ' . $result_nve_atrb['inseridos'] . PHP_EOL;
            $message .= ' - NVE At/Valor - Atualizados: ' . $result_nve_atrV['atualizados'] . ' - Inseridos: ' . $result_nve_atrV['inseridos'] . PHP_EOL;
            $message .= ' - NCMs Válidos II/IPI e NVE - Tabelas auxiliares atualizadas!' . PHP_EOL;

            file_put_contents($filename, $message, FILE_APPEND);
        }
    }

    public function envio_relatorios_destinatarios_tec($ncms_alteradas){

        $count_empresas = 0;
        $count_emails = 0;

        if(!empty($ncms_alteradas)){

            $empresas_aptas = $this->ncm_model->empresas_envio_relatorio_destinatarios_tec();

            $dados_relatorio = [];

            foreach ($empresas_aptas as $empresa) {

                foreach ($ncms_alteradas as $ncm) {

                    $empresa = (array)$empresa;

                    $itens_impactados = $this->ncm_model->itens_envio_relatorio_destinatarios_tec($ncm['cod_ncm'], $empresa['id_empresa']);


                        foreach ($itens_impactados as $item) {

                            $item = (array)$item;
                            

                            $id_empresa = $item['id_empresa'];

                            $dados_relatorio[$id_empresa][] = [
                                'part_number' => $item['part_number'],
                                'descricao_resumida' => $item['descricao_mercado_local'],
                                'nome_fantasia' => $item['nome_fantasia'],
                                'estabelecimento' => $item['estabelecimento'],
                                'cod_ncm' => $ncm['cod_ncm'],
                                'motivo' => $ncm['key'],
                                'de' => $ncm['modificado_de'],
                                'para' => $ncm['modificado_para'],
                            ];
                        }

                }
                
            }

            foreach ($empresas_aptas as $empresa) {

                $itens = $dados_relatorio[$empresa->id_empresa];

                $filename = '';
                $filename = $this->generate_xls_empresa($itens, $empresa->id_empresa);

                if($filename != ''){

                    $email_data['base_url'] = config_item('online_url');
                    $email_data['title'] = 'Alterações na TEC';
                    $email_data['url_download'] = config_item('online_url') . 'assets/tmp/'. $filename;
                    $body = $this->load->view('templates/notificacao_empresa_alteracao_tec', $email_data, TRUE);

                    $emails = explode(';', $empresa->destinatarios_tec);

                    foreach($emails as $email){

                        $this->load->library('email');

                        $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
                        $this->email->to($email);
                        $this->email->subject('[Gestão Tarifária] - Alterações na TEC');
                        $this->email->message($body);

                        $this->email->send();

                        $count_emails ++;
                    }
                }

                $count_empresas ++;

            }
        }
        
        return array('empresas_relatorio' => $count_empresas, 'emails_enviados' => $count_emails);

    }

    public function generate_xls_empresa($itens, $id_empresa)
    {
       $sheet = array(
            'headers' => array(
                'PN',
                'Descrição resumida',
                'Estabelecimento',
                'Empresa',
                'NCM impactada',
                'Motivo',
                'De',
                'Para'
            )
        );

        $this->CI->load->library('PHPExcel');

        $excel = new PHPExcel();

        $excel->getActiveSheet()->getStyle('A1:H1')->applyFromArray(array(
            'fill' => array(
                'type' => PHPExcel_Style_Fill::FILL_SOLID,
                'color' => array('rgb' => '4F81BD'),
            ),
            'font' => array(
                'color' => array('rgb' => 'FFFFFF'),
                'bold' => true,
            ),
            'borders' => array(
                'bottom' => array(
                    'style' => PHPExcel_Style_Border::BORDER_THIN,
                    'color' => array('rgb' => '000000'),
                )
            ),
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER,
            ),
        ));

        $excel->getActiveSheet()->getStyle('A1:H1')
            ->getAlignment()
            ->setWrapText(true);

        $excel->getActiveSheet()->fromArray($sheet['headers'], NULL, 'A1');

        $row = 2;

        foreach ($itens as $k => $item)
        {

            $start_row = $row;

            $motivo = '';
            $de = '';
            $para = '';

            if($item['motivo'] == 'desc_ncm'){
                $motivo = 'Alteração da descrição';
                $de = $item['de'];
                $para = $item['para'];
            }
            if($item['motivo'] == 'vigente'){
                $motivo = 'Inativação';
            }
            

            $excel->getActiveSheet()->getCell('A' . $row)->setValueExplicit($item['part_number'], PHPExcel_Cell_DataType::TYPE_STRING);
            $excel->getActiveSheet()->getCell('B' . $row)->setValueExplicit($item['descricao_resumida'], PHPExcel_Cell_DataType::TYPE_STRING);
            $excel->getActiveSheet()->getCell('C' . $row)->setValueExplicit($item['estabelecimento'], PHPExcel_Cell_DataType::TYPE_STRING);
            $excel->getActiveSheet()->getCell('D' . $row)->setValueExplicit($item['nome_fantasia'], PHPExcel_Cell_DataType::TYPE_STRING);
            $excel->getActiveSheet()->getCell('E' . $row)->setValueExplicit($item['cod_ncm'], PHPExcel_Cell_DataType::TYPE_STRING);
            $excel->getActiveSheet()->getCell('F' . $row)->setValueExplicit($motivo, PHPExcel_Cell_DataType::TYPE_STRING);
            $excel->getActiveSheet()->getCell('G' . $row)->setValueExplicit($de, PHPExcel_Cell_DataType::TYPE_STRING);
            $excel->getActiveSheet()->getCell('H' . $row)->setValueExplicit($para, PHPExcel_Cell_DataType::TYPE_STRING);
            

            // Cores alternadas
            $RowBgColor = ($k % 2 ? "DAE7F1" : "B8CCE4");

            $excel->getActiveSheet()->getStyle('A'.$start_row.':H'.$row)->getFill()->applyFromArray(array(
                'type' => PHPExcel_Style_Fill::FILL_SOLID,
                'startcolor' => array(
                    'rgb' => $RowBgColor
                )
            ));

            // Bordas
            $excel->getActiveSheet()->getStyle('A'.$start_row.':H'.$row)->applyFromArray(array(
            'borders' => array(
                    'allborders' => array('style' => PHPExcel_Style_Border::BORDER_THIN)
                )
            ));

            $excel->getActiveSheet()->getStyle('A'.$start_row.':H'.$row)->applyFromArray(array(
            'borders' => array(
                    'top'    => array('style' => PHPExcel_Style_Border::BORDER_MEDIUM),
                    'bottom' => array('style' => PHPExcel_Style_Border::BORDER_MEDIUM)
                )
            ));

            ++$row;
        }

        foreach (range('A', 'H') as $columnID)
        {
            $excel->getActiveSheet()->getColumnDimension($columnID)
                ->setAutoSize(true);
        }

        $filename = 'relatorio-destinatarios-tec-empresa-'.$id_empresa.'-'.date('d-m-Y_H-i-s').'.xlsx';

        $writer = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');

        $excel->setActiveSheetIndex(0);

        $prop = $excel->getProperties();
        $prop->setCompany("Becomex");
        $prop->setCreator("Becomex");
        $prop->setLastModifiedBy("Becomex");
        $prop->setTitle("Documento");
        $prop->setSubject("Documento");
        $prop->setDescription("Becomex - Relatorio Cron");
        $prop->setKeywords("becomex relatorio cron");

        $filepath = FCPATH . 'assets/tmp/' . $filename;
        $writer->save($filepath);

        return $filename;
    }

    public function xls_log($type, $date)
    {
        $this->load->model('tec_log_alteracao_model');

        $this->tec_log_alteracao_model->set_state('filter.date', $date);
        $this->tec_log_alteracao_model->set_tipo_log_alteracao($type);
        $dump = $this->tec_log_alteracao_model->dump_html_log_alteracoes();

        header('Content-type: application/vnd.ms-excel');
        $filename = $type . '_' . date('d_m_Y') . '.xls';
        header('Content-Disposition: attachment; filename=' . $filename);

        $data = '<html xmlns:x="urn:schemas-microsoft-com:office:excel">
        <head>
            <meta http-equiv="content-type" content="application/xhtml+xml; charset=UTF-8" />
            <!--[if gte mso 9]>
            <xml>
                <x:ExcelWorkbook>
                    <x:ExcelWorksheets>
                        <x:ExcelWorksheet>
                            <x:Name>Sheet 1</x:Name>
                            <x:WorksheetOptions>
                                <x:Print>
                                    <x:ValidPrinterInfo/>
                                </x:Print>
                            </x:WorksheetOptions>
                        </x:ExcelWorksheet>
                    </x:ExcelWorksheets>
                </x:ExcelWorkbook>
            </xml>
            <![endif]-->
        </head>
        <body>' . $dump . '</body></html>';

        echo $data;
    }

    public function gerar_xls($base64)
    {

        $decoded = base64_decode($base64);

        $data['consultas'] = array(
            'importacao_cliente' => null, 'xmls_cliente' => null,
            'consultas_rf' => null, 'estatisticas_br' => null,
            'lista_importacoes_ncm' => null
        );

        $values = array();
        parse_str($decoded, $values);

        if (!empty($values)) {
            $this->load->library('form_validation');
            $oracle = $this->load->database('estat_brasil', TRUE);

            $raw_mes_ano = $values['raw_mes_ano'];
            $descricao_produto = $values['descricao_produto'];
            $cnpj_cliente = $values['cnpj_cliente'];

            $importacao_cliente = $values['importacao_cliente'];
            $xmls_cliente = $values['xmls_cliente'];
            $consultas_rf = $values['consultas_rf'];
            $estatisticas_br = $values['estatisticas_br'];
            $lista_importacoes_ncm = $values['lista_importacoes_ncm'];

            $mes_ano = explode("-", $raw_mes_ano);
            $mes_ano = sprintf("%04d-%02d", $mes_ano[1], $mes_ano[0]);

            $xlsx = array();

            $sheet = array(
                'data' => array(
                    array(
                        $descricao_produto,
                        $cnpj_cliente,
                        date('m/Y', strtotime($mes_ano)),
                        $values['ncm']
                    )
                ),
                'title' => 'Parâmetros da pesquisa',
                'headers' => array(
                    'Descrição do produto' => 'string',
                    'CNPJ' => 'string',
                    'Data' => 'string',
                    'NCM' => 'string',
                    'Número máximo de importações' => 'string'
                )
            );

            if (!empty($values['num_max_importacoes'])) {
                $sheet['data'][] = array(
                    $values['num_max_importacoes']
                );
            }

            $xlsx[] = $sheet;

            if (!empty($importacao_cliente)) {
                $ic_mes_ano = date("Y-m-d", strtotime($mes_ano));

                $str_descricao_produto = strtoupper($descricao_produto);

                $query = $oracle->query("
                    select
                        adicao.cod_ncm, merc.des_mercadoria, merc.num_di,
                        merc.num_adicao, merc.num_seq_merc, di.dat_di
                    from
                        becomex.tb_di di,
                        becomex.tb_di_adicao adicao,
                        becomex.tb_di_adicao_merc merc
                    where
                        di.cnpj_import like '{$cnpj_cliente}%'
                        and di.num_versao = 0 and di.dat_di > TO_DATE('{$ic_mes_ano}', 'YYYY-MM-DD')
                        and adicao.num_di = di.num_di and adicao.num_versao = 0
                        and merc.num_di = di.num_di and merc.num_adicao = adicao.num_adicao
                        and merc.num_versao = 0 and UPPER(merc.des_mercadoria) like '%{$str_descricao_produto}%'
                ");

                $sheet = array(
                    'data' => array(),
                    'title' => 'Importação do Cliente',
                    'headers' => array(
                        'cod_ncm' => 'string',
                        'des_mercadoria' => 'string',
                        'num_di' => 'string',
                        'num_adicao' => 'string',
                        'num_seq_merc' => 'string',
                        'dat_di' => 'string'
                    )
                );

                foreach ($query->result() as $result) {
                    $sheet['data'][] = array(
                        (string) $result->COD_NCM,
                        (string) $result->DES_MERCADORIA,
                        (string) $result->NUM_DI,
                        (string) $result->NUM_ADICAO,
                        (string) $result->NUM_SEQ_MERC,
                        (string) $result->DAT_DI
                    );
                }

                $xlsx[] = $sheet;
            }

            if (!empty($xmls_cliente)) {

                $str_descricao_produto = strtoupper($descricao_produto);

                $query = $oracle->query("
                    select
                        nf.emit_xnome, item.PROD_CPROD COD_PROD,
                        item.prod_ncm, item.PROD_XPROD, item.PROD_VPROD
                    from
                        xmlnf_nf nf,
                        xmlnf_nf_item item
                    where nf.dest_cnpj like '{$cnpj_cliente}%'
                        and item.id_arquivo = nf.id_arquivo
                        and UPPER(item.PROD_XPROD) LIKE ('%{$str_descricao_produto}%')
                    order by 2, 3, 4
                ");

                $sheet = array(
                    'data' => array(),
                    'title' => 'XMLs do Cliente',
                    'headers' => array(
                        'emit_xnome' => 'string',
                        'cod_prod' => 'string',
                        'prod_ncm' => 'string',
                        'prod_xprod' => 'string',
                        'prod_vprod' => 'string'
                    )
                );

                foreach ($query->result() as $result) {
                    $sheet['data'][] = array(
                        (string) $result->EMIT_XNOME,
                        (string) $result->COD_PROD,
                        (string) $result->PROD_NCM,
                        (string) $result->PROD_XPROD,
                        (string) $result->PROD_VPROD
                    );
                }

                $xlsx[] = $sheet;
            }

            if (!empty($consultas_rf)) {
                $query = $oracle->query("
                    SELECT
                        C.COD_NCM, C.NR_DOCTO, C.ORGAO,
                        C.DAT_CONSULTA, C.ASSUNTO, C.EMENTA
                    FROM CONSULTA_SRF C
                    WHERE UPPER(ementa) like UPPER('%{$descricao_produto}%')
                ");

                $sheet = array(
                    'data' => array(),
                    'title' => 'Consulta RF',
                    'headers' => array(
                        'cod_ncm' => 'string',
                        'nr_docto' => 'string',
                        'orgao' => 'string',
                        'dat_consulta' => 'string',
                        'assunto' => 'string',
                        'ementa' => 'string'
                    )
                );

                foreach ($query->result() as $result) {
                    $sheet['data'][] = array(
                        (string) $result->COD_NCM,
                        (string) $result->NR_DOCTO,
                        (string) $result->ORGAO,
                        (string) $result->DAT_CONSULTA,
                        (string) $result->ASSUNTO,
                        (string) $result->EMENTA
                    );
                }

                $xlsx[] = $sheet;
            }

            if (!empty($estatisticas_br)) {
                $eb_mes_ano = date("Ym", strtotime($mes_ano));

                $search_descricao = str_replace(' ', '%', $descricao_produto);

                $query = $oracle->query("
                    select ncm, descricao_produto
                    from estat_brasil.estat_importacao
                    where id_mes_ano >= '{$eb_mes_ano}' and ( upper(descricao_produto) like upper('%{$search_descricao}%') )
                ");

                $sheet = array(
                    'data' => array(),
                    'title' => 'Estatísticas Brasil',
                    'headers' => array(
                        'ncm' => 'string',
                        'descricao_produto' => 'string'
                    )
                );

                foreach ($query->result() as $result) {
                    $desc = preg_replace("/\v/", "", $result->DESCRICAO_PRODUTO);
                    $desc = str_replace("=", "'=", $desc);

                    $sheet['data'][] = array(
                        (string) $result->NCM,
                        (string) $desc
                    );
                }

                $xlsx[] = $sheet;
            }

            if (!empty($lista_importacoes_ncm)) {
                $ncm = $values['ncm'];
                $num_max_importacoes = (int) $values['num_max_importacoes'];

                $id_mes_ano = date("Ym", strtotime($mes_ano));

                $query = $oracle->query("
                    SELECT * FROM (
                        select inner_query.*, rownum rnum FROM
                        (
                            select * from estat_brasil.estat_importacao
                            where ncm = '{$ncm}' and id_mes_ano >= '{$id_mes_ano}'
                        ) inner_query WHERE rownum < {$num_max_importacoes}
                    )
                ");

                $sheet = array(
                    'data' => array(),
                    'title' => 'Importações NCM',
                    'headers' => array(
                        'nr_ordem' => 'string',
                        'id_mes_ano' => 'string',
                        'mes_ano' => 'string',
                        'ncm' => 'string',
                        'descricao_ncm' => 'string',
                        'cod_pais_origem' => 'string',
                        'desc_pais_origem' => 'string',
                        'cod_pais_aquisicao' => 'string',
                        'desc_pais_aquisicao' => 'string',
                        'cod_ume' => 'string',
                        'desc_ume' => 'string',
                        'unidade_comercializacao' => 'string',
                        'descricao_produto' => 'string',
                        'qtde_ume' => 'string',
                        'peso_liquido_kg' => 'string',
                        'val_fob_us' => 'string',
                        'val_frete_us' => 'string',
                        'val_seguro_us' => 'string',
                        'val_unit_produto_us' => 'string',
                        'qtde_comerc' => 'string',
                        'val_total_produto_us' => 'string'
                    )
                );

                foreach ($query->result() as $result) {
                    $sheet['data'][] = array(
                        (string) $result->NR_ORDEM,
                        (string) $result->ID_MES_ANO,
                        (string) $result->MES_ANO,
                        (string) $result->NCM,
                        (string) $result->DESCRICAO_NCM,
                        (string) $result->COD_PAIS_ORIGEM,
                        (string) $result->DESC_PAIS_ORIGEM,
                        (string) $result->COD_PAIS_AQUISICAO,
                        (string) $result->DESC_PAIS_AQUISICAO,
                        (string) $result->COD_UME,
                        (string) $result->DESC_UME,
                        (string) $result->UNIDADE_COMERCIALIZACAO,
                        (string) $result->DESCRICAO_PRODUTO,
                        (string) $result->QTDE_UME,
                        (string) $result->PESO_LIQUIDO_KG,
                        (string) $result->VAL_FOB_US,
                        (string) $result->VAL_FRETE_US,
                        (string) $result->VAL_SEGURO_US,
                        (string) $result->VAL_UNIT_PRODUTO_US,
                        (string) $result->QTDE_COMERC,
                        (string) $result->VAL_TOTAL_PRODUTO_US
                    );
                }

                $xlsx[] = $sheet;
            }

            $this->load->library('xlsxwriter');

            $this->xlsxwriter->setAuthor('Becomex Consulting');

            $writer = new XLSXWriter();

            foreach ($xlsx as $s) {
                $writer->writeSheet($s['data'], $s['title'], $s['headers']);
            }

            $path = FCPATH . 'assets/base_estatisticas/';

            if (!is_dir($path)) {
                mkdir($path);
            }

            $filename = 'base-estatisticas-' . date('d-m-Y_H-i-s') . '.xlsx';

            $xlsx_data = $writer->writeToString($filename);
            file_put_contents($path . $filename, $xlsx_data);

            $email_data['base_url'] = config_item('online_url');
            $email_data['title'] = 'Consulta finalizada';
            $email_data['url_download'] = config_item('online_url') . 'assets/base_estatisticas/' . $filename;

            $email_data['descricao_produto'] = $descricao_produto;
            $email_data['data_pesquisada'] = date('m/Y', strtotime($mes_ano));
            $email_data['cnpj_cliente'] = $values['cnpj_cliente'];
            $email_data['ncm'] = $values['ncm'];
            $email_data['num_max_importacoes'] = $values['num_max_importacoes'];

            $html = $this->load->view('templates/consulta_base_estatistica', $email_data, TRUE);

            $this->load->model('usuario_model');
            $usuario = $this->usuario_model->get_entry($values['id_usuario']);

            $this->load->library('email');

            $this->email->set_crlf("\r\n");

            $this->email->from(config_item('mail_from_addr'));
            $this->email->to($usuario->email);

            if (!empty($lista_importacoes_ncm)) {
                $this->email->subject('[Gestão Tarifária] - Bases estatísticas - NCM ' . $values['ncm']);
            } else {
                $this->email->subject('[Gestão Tarifária] - Bases estatísticas - ' . $descricao_produto . ' - CNPJ ' . $cnpj_cliente);
            }

            $this->email->message($html);
            $this->email->send();
        }
    }

    public function clean_html_grupos()
    {
        $this->debug('Limpeza iniciada - ' . date('Y-m-d H:i:s'));

        $query = $this->db->get('grupo_tarifario');

        foreach ($query->result() as $grupo) {
            $dbdata['subsidio']               = strip_tags($grupo->subsidio);
            $dbdata['memoria_classificacao']  = strip_tags($grupo->memoria_classificacao);
            $dbdata['dispositivo_legal']      = strip_tags($grupo->dispositivo_legal);
            $dbdata['solucao_consulta']       = strip_tags($grupo->solucao_consulta);
            $dbdata['caracteristica']         = strip_tags($grupo->caracteristica);

            $this->db->update('grupo_tarifario', $dbdata, array('id_grupo_tarifario' => $grupo->id_grupo_tarifario));

            $this->debug('[Grupo ' . $grupo->id_grupo_tarifario . '] Limpeza dos campos finalizada.');
        }

        $this->debug('Limpeza finalizada - ' . date('Y-m-d H:i:s'));
    }

    public function import_cest()
    {
        if (!$this->input->is_cli_request()) {
            redirect();
        }

        $row = 0;
        $csv = FCPATH . 'assets/cest/cest.csv';

        if (($handle = fopen($csv, "r")) !== FALSE) {
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $row++;

                if ($row === 1) continue;

                $cod_cest = $data[0];
                $ncms = $data[1];
                $desc_cest = $data[2];

                $f_cod_cest = preg_replace('/\D/', '', $cod_cest);

                $this->db->where('cod_cest', $f_cod_cest);
                $q1 = $this->db->count_all_results('cest');

                if ($q1 == 0) {
                    $dbdata = array(
                        'cod_cest' => $f_cod_cest,
                        'descricao' => $desc_cest
                    );

                    $this->db->insert('cest', $dbdata);
                }

                $f_ncms = explode(",", $ncms);

                foreach ($f_ncms as $ncm) {
                    $f_ncm = preg_replace('/\D/', '', $ncm);
                    $f_ncm = str_pad($f_ncm, 2, '0', STR_PAD_LEFT);

                    $this->db->set('cod_cest', $f_cod_cest);
                    $this->db->set('cod_ncm', $f_ncm);
                    $this->db->replace('cest_ncm');
                }
            }

            fclose($handle);
        }
    }

    public function envia_email_itens_homologacao_semanal()
    {
        // Resolve o problema de memory leak
        $this->db->save_queries = FALSE;
        $this->db->db_debug     = FALSE;

        $this->load->library('benchmark');

        $this->benchmark->mark('code_start');

        $this->debug('Iniciando processo - ' . date('Y-m-d H:i:s'));

        $this->load->model([
            'empresa_model', 'cad_item_model', 'usuario_model'
        ]);

        $this->empresa_model->set_state('filter.recebe_email_pendencias', 1);

        $empresas = $this->empresa_model->get_entries();

        foreach ($empresas as $empresa) {

            //verificando qual tipo de homologação a empresa tem
            $homologacoes = $this->empresa_model->get_homologacao_by_id_empresa($empresa->id_empresa);

            //setando filtros
            $this->cad_item_model->set_state('filter.id_empresa', $empresa->id_empresa);
            $this->cad_item_model->set_state('filter.list_opt', 'homologar');
            $this->cad_item_model->set_state('filter.semanal', '1');

            $itens = $this->cad_item_model->get_entries_by_user_count($homologacoes);

            $id_usuarios = [];
            foreach ($itens as $item) {
                if (array_key_exists($item->id_resp_engenharia, $id_usuarios)) {
                    $id_usuarios[$item->id_resp_engenharia] += 1;
                } else {
                    $id_usuarios[$item->id_resp_engenharia] = 1;
                }
            }

            $this->debug(count($itens) . ' - itens - Empresa: ' . $empresa->razao_social);
            $this->debug('---------------- itens usuario resp');
            foreach ($id_usuarios as $k => $v) {
                $usuario = $this->usuario_model->get_entry($k);

                if ($this->send_mail_item_homologacao($usuario, $v, true)) {
                    $this->debug('Email enviado para o usuário: ' . $usuario->email);
                } else {
                    $this->debug('Ocorreu erro ao enviar email para o usuário: ' . $usuario->email);
                }

                $this->debug('usuario: ' . $k . ' - itens: ' . $v);
            }


            $this->debug('---------------- itens usuario resp');
        }

        $this->debug('Processo finalizado - ' . date('Y-m-d H:i:s'));

        $this->benchmark->mark('code_end');
    }

    public function envia_email_itens_homologacao()
    {
        // Resolve o problema de memory leak
        $this->db->save_queries = FALSE;
        $this->db->db_debug     = FALSE;

        $this->load->library('benchmark');

        $this->benchmark->mark('code_start');

        $this->debug('Iniciando processo - ' . date('Y-m-d H:i:s'));

        $this->load->model([
            'empresa_model', 'cad_item_model', 'usuario_model'
        ]);

        $this->empresa_model->set_state('filter.recebe_email_pendencias', 1);

        $empresas = $this->empresa_model->get_entries();

        foreach ($empresas as $empresa) {

            //verificando qual tipo de homologação a empresa tem
            $homologacoes = $this->empresa_model->get_homologacao_by_id_empresa($empresa->id_empresa);

            //setando filtros
            $this->cad_item_model->set_state('filter.id_empresa', $empresa->id_empresa);
            $this->cad_item_model->set_state('filter.list_opt', 'homologar');
            $this->cad_item_model->set_state('filter.semanal', 0);

            $itens = $this->cad_item_model->get_entries_by_user_count($homologacoes);

            $id_usuarios = [];
            foreach ($itens as $item) {
                $this->db->where('id_item', $item->id_item);
                $this->db->where('part_number', $item->part_number);
                $this->db->where('id_empresa', $item->id_empresa);
                $this->db->update('cad_item', array('envia_email_notificacao' => 1));

                if (array_key_exists($item->id_resp_engenharia, $id_usuarios)) {
                    $id_usuarios[$item->id_resp_engenharia] += 1;
                } else {
                    $id_usuarios[$item->id_resp_engenharia] = 1;
                }
            }

            $this->debug(count($itens) . ' - itens - Empresa: ' . $empresa->razao_social);
            // $this->debug('homologações:');

            // foreach($homologacoes as $key => $v) {
            //     $this->debug($key . ' - ' . ($v ? 'SIM' : 'NÃO'));
            // }

            $this->debug('---------------- itens usuario resp');
            foreach ($id_usuarios as $k => $v) {
                $usuario = $this->usuario_model->get_entry($k);

                if ($this->send_mail_item_homologacao($usuario, $v)) {
                    $this->debug('Email enviado para o usuário: ' . $usuario->email);
                } else {
                    $this->debug('Ocorreu erro ao enviar email para o usuário: ' . $usuario->email);
                }

                $this->debug('usuario: ' . $k . ' - itens: ' . $v);
            }


            $this->debug('---------------- itens usuario resp');
        }

        $this->debug('Processo finalizado - ' . date('Y-m-d H:i:s'));

        $this->benchmark->mark('code_end');
    }

    private function send_mail_item_homologacao($usuario, $itens, $semanal = false)
    {
        if (!$usuario->recebe_email_pendencias) {
            return false;
        }
        $url = config_item('online_url') . '/homologacao';

        if ($semanal) {
            $html_message = '
                <h3>Relatório semanal de itens pendentes para homologação:</h3>

                <br>

                <p>Olá, ' . $usuario->nome . '!</p>
                <p>Você possui homologações pendentes no portal, verifique abaixo as informações: </p>

                <div class="panel panel-default">
                    <div class="panel-body" style="padding: 20px;border-left: 3px solid #ccc;background: #FBFBFB;">
                        <p><strong>Quantidade de itens: </strong> ' . $itens . '</p>
                    </div>
                </div>

                <p style="text-align: right;"><a href="' . $url . '">Clique aqui para acompanhar no portal</a></p>
            ';
        } else {
            $html_message = '
                <h3>Novos itens pendentes para homologação:</h3>

                <br>

                <p>Olá, ' . $usuario->nome . '!</p>
                <p>Você possui novas homologações pendentes no portal, verifique abaixo as informações: </p>

                <div class="panel panel-default">
                    <div class="panel-body" style="padding: 20px;border-left: 3px solid #ccc;background: #FBFBFB;">
                        <p><strong>Quantidade de itens: </strong> ' . $itens . '</p>
                    </div>
                </div>

                <p style="text-align: right;"><a href="' . $url . '">Clique aqui para acompanhar no portal</a></p>
            ';
        }

        $temp_data = array(
            'base_url' => config_item('online_url'),
            'html_message' => $html_message
        );

        $body = $this->load->view('templates/basic_template', $temp_data, TRUE);

        $this->load->library('email');

        $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
        $this->email->to($usuario->email);
        $this->email->subject('[Gestão Tarifária] - Novas Homologações Pendentes');
        $this->email->message($body);

        return $this->email->send();
    }

}
