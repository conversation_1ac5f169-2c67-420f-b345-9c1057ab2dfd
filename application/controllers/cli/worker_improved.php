<?php
// application/controllers/cli/worker_improved.php

defined('BASEPATH') || exit('No direct script access allowed');

use App\Exceptions\EmailQueueException;
use App\Handlers\EmailHandlerInterface;

/**
 * Worker melhorado para processamento de fila usando apenas handlers específicos.
 *
 * Esta versão utiliza exclusivamente classes de handler para melhor organização
 * e manutenibilidade do código, removendo métodos específicos do worker.
 *
 * @property CI_Input $input
 * @property CI_Loader $load
 * @property Email_queue_model $email_queue_model
 */
class Worker_improved extends CI_Controller
{
    /**
     * @var array Mapeia tipos de jobs para suas respectivas classes de handler
     */
    private $handler_classes = [
        'alteracao_status_massa' => 'AlteracaoStatusMassaHandler',
        'alteracao_status_by_ncm' => 'AlteracaoStatusMassaHandler',
        'homologacao_massa' => 'HomologacaoMassaHandler',
        // Adicione novos handlers aqui conforme necessário
    ];

    /**
     * @var string Timestamp de início da execução para logs
     */
    private $execution_start_time;

    /**
     * @var int Contador de jobs processados com sucesso
     */
    private $successful_jobs = 0;

    /**
     * @var int Contador de jobs que falharam
     */
    private $failed_jobs = 0;

    public function __construct()
    {
        parent::__construct();
        if (!$this->input->is_cli_request()) {
            show_error('Este script só pode ser executado via linha de comando.', 403);
            exit;
        }
        $this->load->model('email_queue_model');
        $this->execution_start_time = date('Y-m-d H:i:s');
    }

    /**
     * Processa a fila de jobs, atuando como um despachante (dispatcher).
     *
     * @param int $limit O número máximo de jobs a serem processados nesta execução.
     */
    public function process_email_queue($limit = 10)
    {
        $this->logExecutionStart($limit);

        $pending_jobs = $this->email_queue_model->get_pending_emails($limit);

        if (empty($pending_jobs)) {
            $this->logNoJobsFound();
            return;
        }

        $this->logJobsFound(count($pending_jobs));

        foreach ($pending_jobs as $job) {
            $this->processSingleJob($job);
        }

        $this->logExecutionSummary();
    }

    /**
     * Processa um único job da fila
     *
     * @param object $job
     */
    private function processSingleJob($job)
    {
        $this->email_queue_model->mark_as_processing($job->id);
        $this->logJobStart($job->id);

        try {
            $payload = $this->validateAndParsePayload($job);
            $this->dispatchJob($payload['type'], $payload);

            $this->email_queue_model->mark_as_sent($job->id);
            $this->logJobSuccess($job->id, $payload['type']);
            $this->successful_jobs++;
        } catch (EmailQueueException $e) {
            $this->handleJobFailure($job->id, $e->getMessage());
        } catch (Exception $e) {
            $this->handleJobFailure($job->id, 'Erro inesperado: ' . $e->getMessage());
        }
    }

    /**
     * Valida e faz parse do payload do job
     *
     * @param object $job
     * @return array
     * @throws EmailQueueException
     */
    private function validateAndParsePayload($job)
    {
        if (empty($job->payload)) {
            throw new EmailQueueException('Payload vazio encontrado no job.');
        }

        $payload = json_decode($job->payload, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new EmailQueueException('Erro ao decodificar JSON do payload: ' . json_last_error_msg());
        }

        if (empty($payload) || !isset($payload['type'])) {
            throw new EmailQueueException('O tipo de job (type) não foi definido no payload.');
        }

        return $payload;
    }

    /**
     * Despacha o job usando o handler apropriado
     *
     * @param string $type
     * @param array $payload
     * @throws EmailQueueException
     */
    private function dispatchJob($type, $payload)
    {
        if (!isset($this->handler_classes[$type])) {
            throw new EmailQueueException("Tipo de job desconhecido: {$type}. Tipos disponíveis: " . implode(', ', array_keys($this->handler_classes)));
        }

        $handlerClass = $this->handler_classes[$type];

        try {
            $this->load->handler($handlerClass);
            $handlerVar = strtolower($handlerClass);
            $handler = $this->$handlerVar;

            $this->validateHandler($handler, $handlerClass);
            $handler->handle($payload);
            $this->logHandlerSuccess($type, $handlerClass);
        } catch (Exception $e) {
            $this->logHandlerError($type, $handlerClass, $e->getMessage());
            throw new EmailQueueException("Erro ao processar job com handler {$handlerClass}: " . $e->getMessage());
        }
    }

    /**
     * Valida se o handler está corretamente implementado
     *
     * @param mixed $handler
     * @param string $handlerClass
     * @throws EmailQueueException
     */
    private function validateHandler($handler, $handlerClass)
    {
        if (!is_object($handler)) {
            throw new EmailQueueException("Handler {$handlerClass} não foi carregado corretamente");
        }

        if (!$handler instanceof EmailHandlerInterface) {
            throw new EmailQueueException("Handler {$handlerClass} deve implementar EmailHandlerInterface");
        }

        if (!method_exists($handler, 'handle')) {
            throw new EmailQueueException("Handler {$handlerClass} deve implementar o método handle()");
        }

        if (!method_exists($handler, 'validatePayload')) {
            throw new EmailQueueException("Handler {$handlerClass} deve implementar o método validatePayload()");
        }
    }

    /**
     * Trata falhas no processamento do job
     *
     * @param int $job_id
     * @param string $error_message
     */
    private function handleJobFailure($job_id, $error_message)
    {
        $this->email_queue_model->mark_as_failed($job_id, $error_message);
        $this->logJobFailure($job_id, $error_message);
        $this->failed_jobs++;
    }

    // ========================================================================
    // MÉTODOS DE LOG E MONITORAMENTO
    // ========================================================================

    /**
     * Log do início da execução
     */
    private function logExecutionStart($limit)
    {
        $message = "[{$this->execution_start_time}] Iniciando processamento da fila (limite: {$limit})";
        echo $message . "\n";
        log_message('info', $message);
    }

    /**
     * Log quando nenhum job é encontrado
     */
    private function logNoJobsFound()
    {
        $message = "Nenhum job pendente para processar";
        echo $message . "\n";
        log_message('info', $message);
    }

    /**
     * Log quando jobs são encontrados
     */
    private function logJobsFound($count)
    {
        $message = "Encontrados {$count} jobs para processar";
        echo $message . "\n";
        log_message('info', $message);
    }

    /**
     * Log do início do processamento de um job
     */
    private function logJobStart($job_id)
    {
        $message = "Processando job #{$job_id}";
        echo $message . "\n";
        log_message('debug', $message);
    }

    /**
     * Log de sucesso de um job
     */
    private function logJobSuccess($job_id, $type)
    {
        $message = "Job #{$job_id} ({$type}) concluído com sucesso";
        echo "✓ " . $message . "\n";
        log_message('info', $message);
    }

    /**
     * Log de falha de um job
     */
    private function logJobFailure($job_id, $error_message)
    {
        $message = "Falha no job #{$job_id}: {$error_message}";
        echo "✗ " . $message . "\n";
        log_message('error', $message);
    }

    /**
     * Log de sucesso do handler
     */
    private function logHandlerSuccess($type, $handler_class)
    {
        $message = "Handler {$handler_class} processou job tipo '{$type}' com sucesso";
        log_message('debug', $message);
    }

    /**
     * Log de erro do handler
     */
    private function logHandlerError($type, $handler_class, $error)
    {
        $message = "Erro no handler {$handler_class} para tipo '{$type}': {$error}";
        log_message('error', $message);
    }

    /**
     * Log do resumo da execução
     */
    private function logExecutionSummary()
    {
        $execution_time = date('Y-m-d H:i:s');
        $total_jobs = $this->successful_jobs + $this->failed_jobs;

        $summary = [
            "Execução finalizada em: {$execution_time}",
            "Total de jobs processados: {$total_jobs}",
            "Sucessos: {$this->successful_jobs}",
            "Falhas: {$this->failed_jobs}"
        ];

        foreach ($summary as $line) {
            echo $line . "\n";
            log_message('info', $line);
        }

        if ($this->failed_jobs > 0) {
            $warning = "ATENÇÃO: {$this->failed_jobs} jobs falharam. Verifique os logs para detalhes.";
            echo "⚠ " . $warning . "\n";
            log_message('warning', $warning);
        }
    }
}
