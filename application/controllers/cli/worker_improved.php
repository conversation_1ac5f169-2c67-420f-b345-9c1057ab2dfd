<?php
// application/controllers/cli/worker_improved.php

defined('BASEPATH') || exit('No direct script access allowed');

use App\Exceptions\EmailQueueException;
use App\Handlers\EmailHandlerInterface;

/**
 * Worker melhorado com suporte a handlers específicos.
 *
 * Esta versão mantém compatibilidade com o padrão atual mas permite
 * o uso de classes de handler específicas para melhor organização.
 *
 * @property CI_Input $input
 * @property CI_Loader $load
 * @property Email_queue_model $email_queue_model
 * @property CadItemWfAtributoService $caditemwfatributoservice
 */
class Worker_improved extends CI_Controller
{
    /**
     * @var array Mapeia os tipos de trabalho para seus respectivos métodos manipuladores (handlers).
     */
    private $handlers = [
        'alteracao_status_massa' => 'handle_alteracao_status_massa',
        'alteracao_status_by_ncm' => 'handle_alteracao_status_by_ncm',
    ];

    /**
     * @var array Mapeia tipos para classes de handler
     */
    private $handler_classes = [
        'alteracao_status_massa' => 'AlteracaoStatusMassaHandler',
        'alteracao_status_by_ncm' => 'AlteracaoStatusMassaHandler' // Reutiliza o mesmo handler
       // 'exportacao_planilha' => 'ExportacaoPlanilhaHandler', // Para futuro uso
    ];

    /**
     * @var bool Define se deve usar handlers específicos ou métodos internos
     */
    private $use_specific_handlers = true;

    public function __construct()
    {
        parent::__construct();
        if (!$this->input->is_cli_request()) {
            show_error('Este script só pode ser executado via linha de comando.', 403);
            exit;
        }
        $this->load->model('email_queue_model');
        $this->load->service('CadItemWfAtributoService');
    }

    /**
     * Processa a fila de e-mails, atuando como um despachante (dispatcher).
     *
     * @param int $limit O número máximo de e-mails a serem processados nesta execução.
     */
    public function process_email_queue($limit = 10)
    {
        echo "Iniciando processamento da fila de e-mails...\n";
        
        $pending_emails = $this->email_queue_model->get_pending_emails($limit);
        
        if (empty($pending_emails)) {
            echo "Nenhum e-mail pendente para processar.\n";
            return;
        }

        foreach ($pending_emails as $job) {
            $this->process_single_job($job);
        }
        
        echo "Processamento da fila concluído.\n";
    }

    /**
     * Processa um único job da fila de e-mails
     *
     * @param object $job
     */
    private function process_single_job($job)
    {
        $this->email_queue_model->mark_as_processing($job->id);
        echo "Processando job #{$job->id}...\n";

        try {
            $payload = $this->validate_and_parse_payload($job);
            $this->dispatch_email($payload['type'], $payload);
            
            $this->email_queue_model->mark_as_sent($job->id);
            echo "Job #{$job->id} concluído com sucesso.\n";
            
        } catch (EmailQueueException $e) {
            $this->handle_job_failure($job->id, $e->getMessage());
        } catch (Exception $e) {
            $this->handle_job_failure($job->id, 'Erro inesperado: ' . $e->getMessage());
        }
    }

    /**
     * Valida e faz parse do payload do job
     *
     * @param object $job
     * @return array
     * @throws EmailQueueException
     */
    private function validate_and_parse_payload($job)
    {
        $payload = json_decode($job->payload, true);
        
        if (empty($payload) || !isset($payload['type'])) {
            throw new EmailQueueException('O tipo de e-mail (type) não foi definido no payload.');
        }
        
        return $payload;
    }

    /**
     * Despacha o e-mail usando o handler apropriado
     *
     * @param string $type
     * @param array $payload
     */
    private function dispatch_email($type, $payload)
    {
        if ($this->use_specific_handlers &&
            $this->processWithSpecificHandler($type, $payload)) {
                
            echo "Job processado com handler específico.\n";
        } else {
            $this->processWithTraditionalHandler($type, $payload);
            echo "Job processado com handler tradicional.\n";
        }
    }

    /**
     * Trata falhas no processamento do job
     *
     * @param int $job_id
     * @param string $error_message
     */
    private function handle_job_failure($job_id, $error_message)
    {
        $this->email_queue_model->mark_as_failed($job_id, $error_message);
        echo "Falha no job #{$job_id}: {$error_message}\n";
    }

    /**
     * Tenta processar usando um handler específico.
     *
     * @param string $type
     * @param array $payload
     * @return bool True se processado com sucesso, false se handler não encontrado
     */
    private function processWithSpecificHandler($type, $payload)
    {
        if (!isset($this->handler_classes[$type])) {
            return false;
        }

        $handlerClass = $this->handler_classes[$type];

        try {
            // Usar o novo método handler() do Loader
            $this->load->handler($handlerClass);

            // Acessar o handler carregado
            $handlerVar = strtolower($handlerClass);
            $handler = $this->$handlerVar;

            $handler->handle($payload);
            return true;
        } catch (Exception $e) {
            log_message('error', 'Erro ao carregar handler: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Processa usando o método tradicional (fallback).
     *
     * @param string $type
     * @param array $payload
     * @throws EmailQueueException
     */
    private function processWithTraditionalHandler($type, $payload)
    {
        // Verifica se existe um handler mapeado para o tipo de job
        if (!isset($this->handlers[$type])) {
            throw new EmailQueueException("Tipo de e-mail desconhecido: {$type}");
        }

        $handler_method = $this->handlers[$type];

        // Verifica se o método do handler realmente existe na classe
        if (!method_exists($this, $handler_method)) {
            throw new EmailQueueException("Manipulador '{$handler_method}' não encontrado para o tipo: {$type}");
        }

        // Chama o método do handler dinamicamente
        $this->$handler_method($payload);
    }



    /**
     * Manipulador tradicional para o e-mail de alteração de status em massa.
     * @param array $payload Os dados necessários para o envio do e-mail.
     * @throws EmailQueueException
     */
    private function handle_alteracao_status_massa($payload)
    {
        if (
            !isset($payload['item_ids']) ||
            !isset($payload['id_empresa']) ||
            !isset($payload['status_id'])
        ) {
            throw new EmailQueueException('Payload inválido para o tipo alteracao_status_massa.');
        }

        $this->caditemwfatributoservice->enviarAlteracaoStatusMassa(
            $payload['item_ids'],
            $payload['id_empresa'],
            $payload['status_id']
        );
    }

    /**
     * Manipulador tradicional para o e-mail de alteração de status por NCM.
     * @param array $payload Os dados necessários para o envio do e-mail.
     * @throws EmailQueueException
     */
    private function handle_alteracao_status_by_ncm($payload)
    {
        if (
            !isset($payload['item_ids']) ||
            !isset($payload['id_empresa']) ||
            !isset($payload['status_id'])
        ) {
            throw new EmailQueueException('Payload inválido para o tipo alteracao_status_massa.');
        }

        $this->caditemwfatributoservice->enviarAlteracaoStatusMassa(
            $payload['item_ids'],
            $payload['id_empresa'],
            $payload['status_id']
        );
    }
}
