<?php

if (!class_exists('Tec_log_alteracao_model')) {
    include APPPATH . 'models/tec_log_alteracao_model' . EXT;
}

class Ncm_model extends Tec_log_alteracao_model
{

    public $_table = 'ncm';
    public $_table_impostos = 'ncm_impostos';
    public $_table_impostos_historico = 'ncm_impostos_2012';
    public $_table_impostos_historico_2017 = 'ncm_impostos_2017';
    public $_table_classificacao_energetica = 'classificacao_energetica';

    public function __construct()
    {
        parent::__construct();

        $this->set_tipo_log_alteracao('ncm_impostos');
    }

    public function get_entry($ncm)
    {
        $ncm = $this->filter_ncm($ncm);
        $this->db->where('replace(codigo, \'.\', \'\') =', $ncm, FALSE);
        $query = $this->db->get('ncm');
        if ($query->num_rows()) {
            $row = $query->row();
            return $row;
        }

        throw new Exception('Código de NCM não cadastrado no sistema');
    }

    public function filter_ncm($ncm)
    {
        return trim(str_replace('.', '', $ncm));
    }

    public function get_ncm_info_by_reg($reg)
    {
        $this->db->where('reg', $reg);
        $query = $this->db->get($this->_table);
        return $query->row();
    }

    public function get_ncm_treeview($ncm, $historico = FALSE)
    {
        $ncm = $this->filter_ncm($ncm);

        $this->db->select(
            '
            imp.*, imp.cod_ncm as ncm, imp.desc_ncm as descricao,
            (select c.cod_cest from cest c join cest_ncm cn on cn.cod_cest = c.cod_cest where cn.cod_ncm = imp.cod_ncm limit 1) as has_cod_cest'
        );

        $this->db->like('cod_ncm', $ncm, 'after', FALSE);
        $this->db->order_by('ncm', 'ASC');

        $this->db->where('num_versao', 0);
        $this->db->where('vigente', 'S');

        if ($historico == '2012') {
            $query = $this->db->get($this->_table_impostos_historico . ' imp');
        } else if ($historico == '2017') {
            $query = $this->db->get($this->_table_impostos_historico_2017 . ' imp');
        } else {
            $query = $this->db->get($this->_table_impostos . ' imp');
        }

        $result = $query->result();

        $opt = array();

        foreach ($result as &$r) {
            $opt[$r->ncm] = 1;

            list($class1, $class2, $class3) = sscanf($r->ncm, '%04s%02s%02s');

            $r->ncm_edit = $class1 . (isset($class2) ? '.' . $class2 : '') . (isset($class3) ? '.' . $class3 : '');

            $r->parent = NULL;

            for ($i = strlen($r->ncm) - 1; $i >= 0; $i--) {
                $check = substr($r->ncm, 0, $i);
                if (isset($opt[$check])) {
                    $r->parent = $check;
                    break;
                }
            }
        }

        return $result;
    }

    public function get_entries_levels($ncm, $historico = FALSE)
    {
        $ncmarr = array();

        for ($i = 0; $i < 8; $i++) {
            $ncmarr[$i] = ($i == 0 ? $ncm : substr($ncmarr[$i - 1], 0, -1));
        }

        $this->db->select('imp.*, imp.cod_ncm as ncm, imp.desc_ncm as descricao');

        $this->db->where_in('cod_ncm', $ncmarr);
        $this->db->order_by('ncm', 'ASC');

        $this->db->where('num_versao', 0);
        $this->db->where('vigente', 'S');

        if ($historico == '2012') {
            $query = $this->db->get($this->_table_impostos_historico . ' imp');
        } else if ($historico == '2017') {
            $query = $this->db->get($this->_table_impostos_historico_2017 . ' imp');
        } else {
            $query = $this->db->get($this->_table_impostos . ' imp');
        }

        return $query->result();
    }

    public function get_secao($codigo)
    {
        $this->db->select('ncm.descricao, ncm.codigo2 as codigo, ncm.secao');
        $this->db->where('ncm.secao', $codigo);
        $this->db->where("(ncm.codigo IS NULL OR codigo = '')", NULL, FALSE);

        $query = $this->db->get('ncm');

        return $query->row();
    }

    public function get_entries_levels_old($ncm)
    {

        $ncm = $this->filter_ncm($ncm);

        $entries = array();
        $filho = NULL;

        do {

            if ($filho != NULL) {
                $this->db->where('reg', $filho);
            } else {
                $this->db->where("replace(codigo, '.', '') = '{$ncm}'", FALSE, FALSE);
            }

            $this->db->where('descta2 <>', 'NCM');

            $query = $this->db->get($this->_table, 1);
            if ($query->num_rows()) {
                $row = $query->row();
                $filho = $row->filho;
                $entries[] = $row;
            } else {
                $filho = NULL;
            }
        } while ($filho !== NULL);

        $entries = array_reverse($entries);

        return $entries;
    }

    public function get_impostos($ncm, $historico = FALSE)
    {
        $ncm = $this->filter_ncm($ncm);

        $this->db->where('cod_ncm', $ncm);
        $this->db->where('num_versao', 0);
        $this->db->where('vigente', 'S');
        $this->db->group_by('cod_ncm');

        if ($historico) {
            $query = $this->db->get($this->_table_impostos_historico, 1);
        } else {
            $query = $this->db->get($this->_table_impostos, 1);
        }

        if ($query->num_rows()) {
            return $query->row();
        }

        return NULL;
    }

    public function get_capitulos_base_itens($cenario = 'atual')
    {
        $user_company = sess_user_company();
        $field   = $cenario == 'atual' ? 'item.ncm' : 'ci.ncm_proposto';

        $adwhere = '';

        # Booking (somente itens que já foram homologados)
        // $this->db->where('ci.status_homologacao', 1);
        $this->db->where(
            "EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = ci.id_item AND homologado = 1 AND cih.tipo_homologacao = 'Fiscal') AND EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = ci.id_item AND homologado = 1 AND cih.tipo_homologacao = 'Engenharia')",
            '',
            FALSE
        );

        $this->db->select("LEFT({$field}, 2) AS capitulo, ncm.descricao", FALSE);

        $this->db->join("item", "item.part_number = ci.part_number AND item.id_empresa = ci.id_empresa", "inner");

        $this->db->join("ncm", "ncm.codigo = LEFT({$field}, 2)");

        // Campos não podem ser Nulos
        $this->db->where("ncm.codigo IS NOT NULL", NULL, FALSE);
        $this->db->where("ncm.codigo <>", 0);
        $this->db->where("ncm.codigo <>", '');

        // Length do código do NCM deve ser menor ou igual a 2 (maximizar a performance da busca)
        $this->db->where("LENGTH(ncm.codigo) <= ", 2, FALSE);

        $this->db->where("ci.id_empresa", $user_company);

        $this->db->group_by("capitulo");
        $this->db->order_by("ncm.codigo", "ASC");


        $query = $this->db->get("cad_item ci");

        $lista = $query->result();

        return $lista;
    }

    public function from_oracle_import_tec_ncm()
    {
        // import from oracle
        $offset = 0;
        $limit  = 5000;
        $ncms_alteradas = array();

        $this->alter_ora_sess();

        $query_total = $this->dbora->query("SELECT COUNT(*) as total FROM tec.tec_ncm WHERE vigente <> 'P' AND num_versao = '0'");
        $total       = $query_total->row()->TOTAL;

        $total_paginas = ceil($total / $limit);

        $atualizados = $inseridos = 0;

        for ($i = 0; $i < $total_paginas; $i++) {
            $offset = $i * $limit;
            $offset_plus_limit = $offset + $limit;

            $innerquery = "SELECT * FROM tec.tec_ncm WHERE vigente <> 'P' AND num_versao = '0' ORDER BY cod_ncm DESC";

            $query = $this->dbora->query(
                "SELECT * FROM (select inner_query.*, rownum rnum FROM ($innerquery) inner_query WHERE rownum < {$offset_plus_limit}) WHERE rnum >= {$offset}"
            );

            foreach ($query->result() as $tec) {
                $data                      = array();

                $data['cod_ncm']           = $cod_ncm = $tec->COD_NCM;
                $data['num_versao']        = $tec->NUM_VERSAO;
                $data['data_vigencia_ini'] = $tec->DAT_VIGENCIA_INI;
                $data['pct_ii']            = $tec->PCT_II;
                $data['pct_ii_normal']     = $tec->PCT_II_NORMAL;
                $data['pct_ipi']           = $tec->PCT_IPI;
                $data['pct_ipi_normal']    = $tec->PCT_IPI_NORMAL;
                $data['pct_pis']           = $tec->PCT_PIS;
                $data['pct_cofins']        = $tec->PCT_COFINS;
                $data['unid_med_est']      = $tec->UNID_MED_EST;
                $data['desc_unid_med_est'] = $tec->DES_UNID_MED_EST;
                $data['num_versao_origem'] = $tec->NUM_VERSAO_ORIGEM;
                $data['data_inclusao_reg'] = $tec->DAT_INCLUSAO_REG;
                $data['data_atualizacao']  = $tec->DAT_ATUALIZACAO;
                $data['desc_ncm']          = $tec->DES_NCM;
                $data['vigente']           = $tec->VIGENTE;
                $data['bk']                = $tec->BK;
                $data['bit']               = $tec->BIT;

                // check if item exists
                $query_check_imp = $this->db->get_where(
                    'ncm_impostos',
                    array('cod_ncm' => $tec->COD_NCM, 'num_versao' => $tec->NUM_VERSAO),
                    1
                );

                $check_campos = array(
                    'dat_vigencia_ini',
                    'pct_ii',
                    'pct_ii_normal',
                    'pct_ipi',
                    'pct_ipi_normal',
                    'pct_pis',
                    'pct_cofins',
                    'unid_med_est',
                    'des_unid_med_est',
                    'desc_ncm',
                    'bk',
                    'bit',
                    'vigente'
                );

                if ($query_check_imp->num_rows() > 0) {
                    $modificado_para     = $modificado_de = array();
                    $result_check_imp = $query_check_imp->row();
                    foreach ($data as $key => $value) {
                        
                        if ($value != $result_check_imp->{$key} && in_array($key, $check_campos)) {
                        
                            if($key == 'vigente'){
                                $this->updateItensGrupGt($data['cod_ncm'], $key);

                                $ncms_alteradas[] = [
                                    'cod_ncm' => $tec->COD_NCM,
                                    'key'  => $key,
                                    'num_versao' => $tec->NUM_VERSAO,
                                    'modificado_de' => $result_check_imp->{$key},
                                    'modificado_para' => $value ,
                                    'data_modificacao' => date('Y-m-d H:i:s')
                                ];
                            }
                            $modificado_de[$key] = $result_check_imp->{$key};
                            $modificado_para[$key] = $value;
                            
                            $ncms_alteradas[] = [
                                'cod_ncm' => $tec->COD_NCM,
                                'key'  => $key,
                                'num_versao' => $tec->NUM_VERSAO,
                                'modificado_de' => $result_check_imp->{$key},
                                'modificado_para' => $value ,
                                'data_modificacao' => date('Y-m-d H:i:s')
                            ];
                        }
                        
                    }

                    // Adicionar no log de alterações
                    if (count($modificado_para) > 0 && strlen(trim($tec->COD_NCM)) == 8) {
                        $objeto = new Tec_objeto_chave_ncm_impostos();
                        $objeto->cod_ncm = $tec->COD_NCM;
                        $objeto->num_versao = $tec->NUM_VERSAO;

                        $this->salvar_log_alteracao($cod_ncm, $objeto, $modificado_de, $modificado_para, 'ALTER');

                        $atualizados++;
                    }

                    $this->db->update(
                        'ncm_impostos',
                        $data,
                        array('cod_ncm' => $tec->COD_NCM, 'num_versao' => $tec->NUM_VERSAO)
                    );
                } else {
                    $objeto = new Tec_objeto_chave_ncm_impostos();
                    $objeto->cod_ncm = $tec->COD_NCM;
                    $objeto->num_versao = $tec->NUM_VERSAO;

                    $this->salvar_log_alteracao($cod_ncm, $objeto, array(), $data, 'ADD');

                    $this->db->insert('ncm_impostos', $data);
                    $inseridos++;
                }
            }
        }

        $current_date = date('Y-m-d');

        $this->set_state('filter.date', $current_date);

        // Conta o total de atualizações
        $total_dump = $this->get_date_total_log($current_date);

        if ($total_dump > 0) {
            $data['base_url'] = config_item('online_url');
            $data['title']    = 'Tabela de Impostos NCM';
            $data['count']    = $total_dump;
            $data['url_log']  = $data['base_url'] . 'cron/xls_log/' . $this->tipo_alteracao . '/' . $current_date;

            $data['affected_companies'] = $this->get_affected_companies($current_date);

            $html = $this->load->view('templates/notificacao_update', $data, TRUE);

            $this->load->library('email');

            $this->email->from($this->config->item('mail_from_addr'), $this->config->item('mail_from_name'));
            $this->email->to($this->config->item('mail_rcpt_general'));
            $this->email->subject('[Gestão Tarifária] - Tabela de Impostos NCM');
            $this->email->message($html);

            if ($this->email->send()) {
                $this->email_enviado_log_alteracoes();
            }
        }

        return array('atualizados' => $atualizados, 'inseridos' => $inseridos, 'ncms_alteradas' => $ncms_alteradas);
    }

    public function empresas_envio_relatorio_destinatarios_tec(){

        $this->db->select('e.id_empresa,
                           e.razao_social,
                           e.nome_fantasia,
                           e.destinatarios_tec');
        $this->db->where("e.destinatarios_tec IS NOT NULL", NULL, FALSE);
        $this->db->where("(e.funcoes_adicionais  LIKE '%|destinatatio_tec|%' 
                            OR e.funcoes_adicionais LIKE 'destinatatio_tec|%' 
                            OR e.funcoes_adicionais LIKE '%|destinatatio_tec' 
                            OR e.funcoes_adicionais = 'destinatatio_tec')", NULL, FALSE);

        $query = $this->db->get('empresa e');

        $result = $query->result();

        return $result;
    
    }

    public function itens_envio_relatorio_destinatarios_tec($ncm, $id_empresa){

        $this->db->select('ci.part_number,
                           ci.descricao_mercado_local,
                           ci.id_empresa,
                           e.nome_fantasia,
                           ci.estabelecimento');
        $this->db->join("empresa e", "e.id_empresa = ci.id_empresa", "inner");
        $this->db->where("ci.ncm_proposto", $ncm);
        $this->db->where("ci.id_empresa", $id_empresa);
        $this->db->where("e.destinatarios_tec IS NOT NULL", NULL, FALSE);
        $this->db->where("(e.funcoes_adicionais  LIKE '%|destinatatio_tec|%' 
                            OR e.funcoes_adicionais LIKE 'destinatatio_tec|%' 
                            OR e.funcoes_adicionais LIKE '%|destinatatio_tec' 
                            OR e.funcoes_adicionais = 'destinatatio_tec')", NULL, FALSE);
        $this->db->order_by('ci.id_empresa', 'ASC');
        $this->db->order_by('ci.descricao_mercado_local', 'ASC');

        $query = $this->db->get('cad_item ci');

        $result = $query->result();

        return $result;
    }

    public function update_ncm_validos()
    {
        $this->db->truncate('ncm_valido_ex_ii');
        $this->db->truncate('ncm_valido_ex_ipi');
        $this->db->truncate('ncm_valido_nve');

        $this->db->query("
            insert into ncm_valido_ex_ii (cod_ncm)
            select cod_ncm
            from tec_ncm_ex_tarif ex
            where ex.dat_vigencia_fim >= CURDATE()
            and ex.num_ex is not null
            and ex.cod_tipo_ex < 5
            group by cod_ncm;
        ");

        $this->db->query("
            insert into ncm_valido_ex_ipi (cod_ncm)
            select cod_ncm
            from tec_ncm_ex_tarif ex
            where ex.dat_vigencia_fim >= CURDATE()
            and ex.num_ex is not null
            and ex.cod_tipo_ex > 5
            group by cod_ncm;
        ");

        $this->db->query("
            insert into ncm_valido_nve (cod_ncm)
            select cd_nomenc_ncm
            from nve_atributo nve
            group by cd_nomenc_ncm;
        ");
    }

    public function check_ncm_exists($ncm, $historico = FALSE)
    {
        $ncm = str_replace('.', '', $ncm);
        $this->db->where('cod_ncm', $ncm);

        if ($historico) {
            $query = $this->db->get($this->_table_impostos_historico);
        } else {
            $query = $this->db->get($this->_table_impostos);
        }

        return ($query->num_rows() > 0) ? TRUE : FALSE;
    }

    public function get_entries($ncms = array(), $limit = NULL, $historico = FALSE)
    {
        if (!empty($ncms)) {
            $this->db->where_in('cod_ncm', $ncms);
        }

        $this->db->group_by('cod_ncm');
        $this->db->order_by('num_versao', 'DESC');
        $this->db->order_by('cod_ncm', 'ASC');

        if ($vigente = $this->get_state('filter.vigente')) {
            $this->db->where('vigente', $vigente);
        }

        if ($this->get_state('filter.num_versao') || $this->get_state('filter.num_versao') === 0) {
            $num_versao = $this->get_state('filter.num_versao');
            $this->db->where('num_versao', $num_versao);
        }

        if ($historico == '2012') {
            $query = $this->db->get($this->_table_impostos_historico, $limit);
        } else if ($historico == '2017') {
            $query = $this->db->get($this->_table_impostos_historico_2017, $limit);
        } else {
            $query = $this->db->get($this->_table_impostos, $limit);
        }

        return $query->result();
    }

    public function get_li_oracle($ncm)
    {
        $ncm1 = substr($ncm, 0, 2);
        $ncm2 = substr($ncm, 0, 4);
        $ncm3 = substr($ncm, 0, 6);
        // $query = $this->dbora->query("SELECT * FROM  tec.tec_ncm_tratamento_adm WHERE (cod_ncm = '{$ncm}' OR cod_ncm = '{$ncm1}' OR cod_ncm = '{$ncm2}' OR cod_ncm = '{$ncm3}') AND (DESTAQUE not in ('555') OR DESTAQUE IS NULL) AND (TIPO_TRATAMENTO IN ('MERCADORIA', 'DESTAQUE DE MERCADORIA'))");
        $query = $this->dbora->query("
        SELECT DISTINCT COD_NCM,ABRANGENCIA,TIPO_TRATAMENTO,ORGAO_ANUENTE,FINALIDADE,DESCRICAO, SUBSTR(DAT_INCLUSAO_REG,1,9) as DAT_INCLUSAO_REG ,DESTAQUE FROM  tec.tec_ncm_tratamento_adm 
        WHERE ((cod_ncm = '{$ncm}' OR cod_ncm = '{$ncm1}' OR cod_ncm = '{$ncm2}' OR cod_ncm = '{$ncm3}') 
        AND (TIPO_TRATAMENTO IN ('MERCADORIA', 'DESTAQUE DE MERCADORIA')) )
        ");
        return $query->result();
    }

    public function getLiOracleParams($ncm, $orgaoAnuente, $destaque)
    {
        $ncm1 = substr($ncm, 0, 2);
        $ncm2 = substr($ncm, 0, 4);
        $ncm3 = substr($ncm, 0, 6);

        $orgaoAnuente = trim(html_entity_decode($orgaoAnuente));
        $destaque = trim(html_entity_decode($destaque));

        if ($destaque == '999' || empty($destaque)) {
            $query = $this->dbora->query(
                "SELECT * FROM  
                    tec.tec_ncm_tratamento_adm 
                    WHERE ORGAO_ANUENTE = '{$orgaoAnuente}'
                        AND (
                            COD_NCM = '{$ncm}' OR COD_NCM = '{$ncm1}' OR COD_NCM = '{$ncm2}' OR COD_NCM = '{$ncm3}'
                        ) AND (
                            DESTAQUE not in ('555') OR DESTAQUE IS NULL) AND (TIPO_TRATAMENTO IN ('MERCADORIA', 'DESTAQUE DE MERCADORIA')
                        )"
            );
        } else {
            $query = $this->dbora->query(
                "SELECT * FROM  
                    tec.tec_ncm_tratamento_adm 
                    WHERE ORGAO_ANUENTE = '{$orgaoAnuente}'
                        AND DESTAQUE = '{$destaque}'
                        AND (
                            COD_NCM = '{$ncm}' OR COD_NCM = '{$ncm1}' OR COD_NCM = '{$ncm2}' OR COD_NCM = '{$ncm3}'
                        ) AND (
                            DESTAQUE not in ('555') OR DESTAQUE IS NULL) AND (TIPO_TRATAMENTO IN ('MERCADORIA', 'DESTAQUE DE MERCADORIA')
                        )"
            );
        }
        return $query->row();
    }


    public function get_antidumping_oracle($ncm)
    {
        $query = $this->dbora->query("SELECT a.cod_ncm, a.pais, a.medida, a.dat_vigencia_fim, a.descricao, a.dat_inclusao_reg FROM tec.tec_ncm_defesa_comercial_automatica a WHERE a.cod_ncm = '{$ncm}' order by 1");
        return $query->result();
    }

    public function get_classificacao_energetica($ncm)
    {
        //Verificar com Matheus sobre
        // $ncm1 = substr($ncm, 0, 2);
        $ncm2 = substr($ncm, 0, 4);
        $ncm3 = substr($ncm, 0, 6);
        $this->db->like('ncm', $ncm);
        // $this->db->or_like('ncm', $ncm1 );
        $this->db->or_like('ncm', $ncm2);
        $this->db->or_like('ncm', $ncm3);
        // $this->db->select('t.*', 'DISTINCT t.id');
        $query = $this->db->get($this->_table_classificacao_energetica . ' t');
        return $query->result();
    }

    public function get_classificacao_energetica_id($id)
    {
        //Verificar com Matheus sobre
        $this->db->like('id', $id);
        $query = $this->db->get($this->_table_classificacao_energetica);
        return $query->row();
    }

    public function ncm_has_classificacao_energetica($ncm)
    {
        return !empty($this->get_classificacao_energetica($ncm));
    }

    public function getStringByNcmImposto($ncm) {
        $str = '';
        $ncmAtualImpostos = $this->get_impostos($ncm);
        
        if (isset($ncmAtualImpostos)) {
            if ($ncmAtualImpostos->pct_ii != NULL) {
                $str .= 'II: ' . $ncmAtualImpostos->pct_ii . '%';
            }

            if ($ncmAtualImpostos->pct_ipi != NULL) {
                $str .= ' IPI: ' . $ncmAtualImpostos->pct_ipi . '%';
            }

            if ($ncmAtualImpostos->pct_pis != NULL) {
                $str .= ' PIS: ' . $ncmAtualImpostos->pct_pis . '%';
            }

            if ($ncmAtualImpostos->pct_cofins != NULL) {
                $str .= ' COFINS: ' . $ncmAtualImpostos->pct_cofins . '%';
            }
        }

        return $str;
    }

    private function updateItensGrupGt($ncm, $key){

        $this->db->select("id_usuario");
        $this->db->where('email', '<EMAIL>');
        $this->db->from('usuario');
        $query = $this->db->get();
        $usuario = $query->row();


        $this->db->select("id");
        $this->db->where('slug', 'homologado_em_revisao');
        $this->db->from('status');
        $query = $this->db->get();
        $id_status_homg_rev = $query->row();

        $this->db->select("id");
        $this->db->where('slug', 'homologado');
        $this->db->from('status');
        $query = $this->db->get();
        $id_status_homg = $query->row();

        if(empty($usuario->id_usuario) || empty($id_status_homg->id) || empty($ncm)){
            return;
        }
        
        $this->db->query("INSERT INTO item_log 
                            (
                                id_item,
                                tipo_homologacao,
                                part_number,
                                id_usuario,
                                id_empresa,
                                estabelecimento,
                                titulo,
                                motivo,
                                criado_em,
                                nao_atualiza_item
                            )SELECT ci.id_item,
                                'fiscal',
                                i.part_number,
                                {$usuario->id_usuario},
                                i.id_empresa,
                                i.estabelecimento,
                                'atualizacao',
                                'Alteração do Status: <em>Homologado</em> &rarr; <strong>Homologado em Revisão</strong> <br> <strong>Motivo</strong>: Alteração de informações da NCM na tabela TEC',
                                NOW(),
                                1
                                FROM item i
                            INNER JOIN cad_item ci ON ci.part_number = i.part_number 
                                AND ci.estabelecimento = i.estabelecimento 
                                AND ci.id_empresa = i.id_empresa 
                            INNER JOIN empresa e ON e.id_empresa = i.id_empresa 
                        WHERE i.id_status = {$id_status_homg->id}
                            AND ncm_proposto LIKE '{$ncm}%'
                            AND (e.campos_adicionais LIKE '%|owner|%' 
                            OR e.campos_adicionais LIKE 'owner|%' 
                            OR e.campos_adicionais LIKE '%|owner' 
                            OR e.campos_adicionais = 'owner');");

        $this->db->query("UPDATE item
                            INNER JOIN cad_item ON item.part_number = cad_item.part_number 
                                AND item.estabelecimento = cad_item.estabelecimento 
                                AND item.id_empresa = cad_item.id_empresa
                            INNER JOIN empresa e ON e.id_empresa = item.id_empresa
                            SET item.id_status = {$id_status_homg_rev->id},
                                item.evento = CONCAT(item.evento, ' - ALTERAÇÃO DA TEC'),
                                item.data_modificacao = NOW()
                            WHERE item.id_status = {$id_status_homg->id}
                            AND cad_item.ncm_proposto LIKE '{$ncm}%'
                            AND (e.campos_adicionais LIKE '%|owner|%' 
                            OR e.campos_adicionais LIKE 'owner|%' 
                            OR e.campos_adicionais LIKE '%|owner' 
                            OR e.campos_adicionais = 'owner');");

        if($key == 'vigente'){

            $this->db->query("UPDATE grupo_tarifario
                                SET descricao = CONCAT('<FONT COLOR=''RED''>(TEC 2022 - NCM INATIVADA NA TEC 2022) </FONT>', descricao),
                                ativo = 0
                                WHERE ncm_recomendada LIKE '{$ncm}%'
                                AND descricao NOT LIKE '%<FONT COLOR=''RED''>(TEC 2022 - NCM INATIVADA NA TEC 2022) </FONT>%';");
        }

    }
}
