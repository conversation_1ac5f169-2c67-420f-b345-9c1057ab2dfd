<?php
class Comex_model extends MY_Model
{
    public $_table = 'comex';

    public function __construct()
    {
        parent::__construct();
    }
    
	public function get_entry($part_number, $id_empresa, $estabelecimento = NULL, $get_row = false)
	{
        $this->db->select('comex.ind_ecomex');
 
        $this->db->where('part_number_original', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('unidade_negocio', $estabelecimento);
		$query = $this->db->get($this->_table);
        $result =  $query->row();
        
        return $query->num_rows()  > 0 ? $result->ind_ecomex : ['E'];
    }
    
    public function check_imported($part_number, $id_empresa, $estabelecimento)
	{ 
        $this->db->where('part_number_original', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('unidade_negocio', $estabelecimento);
        $this->db->where('ind_ecomex', 'EI');
		$query = $this->db->get($this->_table);
        return $query->num_rows()  > 0 ? TRUE : FALSE;
    }
}